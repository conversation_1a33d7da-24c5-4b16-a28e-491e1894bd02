import os
from dotenv import load_dotenv
from telegram import Update
from telegram.ext import <PERSON><PERSON>uilder, CommandHandler, ContextTypes
import helper

load_dotenv()
TELEGRAM_TOKEN = os.getenv("TELEGRAM_TOKEN")

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    result = helper.get_new_whitelisted_markets_for_vault()
    await update.message.reply_text(result)

if __name__ == "__main__":
    if not TELEGRAM_TOKEN:
        print("Please set TELEGRAM_TOKEN in your .env file.")
    else:
        app = ApplicationBuilder().token(TELEGRAM_TOKEN).build()
        app.add_handler(CommandHandler("start", start))
        print("Bot is running...")
        app.run_polling()
