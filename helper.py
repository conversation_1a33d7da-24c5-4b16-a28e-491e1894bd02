

import os
from dotenv import load_dotenv
import requests
import json

load_dotenv()
CHAIN_ID = os.getenv("CHAIN_ID")
VAULT_ADDRESS = os.getenv("VAULT_ADDRESS")

API_URL = "https://api.morpho.org/graphql"

VAULT_MARKETS_QUERY = """
query GetMarketsInVault($vaultAddress: String!) {
  vaultByAddress(address: $vaultAddress, chainId: 1) {
    state {
      allocation {
        market {
          uniqueKey
        }
      }
    }
  }
}
"""

WHITELISTED_MARKETS_QUERY = """
query GetAllWhitelistedMarkets {
  markets(where: { whitelisted: true }) {
    items {
      uniqueKey
      state {
        supplyApy
      }
      loanAsset {
        symbol
      }
      collateralAsset {
        symbol
      }
    }
  }
}
"""

def run_query(query, variables=None):
    """Sends a GraphQL query to the API endpoint."""
    headers = {"Content-Type": "application/json"}
    data = {"query": query}
    if variables:
      data["variables"] = variables
    response = requests.post(API_URL, headers=headers, json=data)
    response.raise_for_status()
    return response.json()

def get_vault_markets(vault_address):
    """Returns the set of unique market keys in the vault."""
    vault_data = run_query(VAULT_MARKETS_QUERY, {"vaultAddress": vault_address})
    vault_by_address = vault_data.get('data', {}).get('vaultByAddress')
    if not vault_by_address:
      return None
    state = vault_by_address.get('state', {})
    allocation = state.get('allocation', [])
    return {item.get('market', {}).get('uniqueKey') for item in allocation if item.get('market', {}).get('uniqueKey')}

def get_whitelisted_markets():
    """Returns all whitelisted markets as a list of dicts."""
    whitelisted_data = run_query(WHITELISTED_MARKETS_QUERY)
    return whitelisted_data.get('data', {}).get('markets', {}).get('items', [])

def get_new_whitelisted_markets_for_vault(vault_address=None):
    """
    Returns a formatted string of new whitelisted markets not yet in the vault.
    If vault_address is None, uses VAULT_ADDRESS from env.
    """
    if vault_address is None:
        vault_address = VAULT_ADDRESS
    try:
        vault_markets_unique_keys = get_vault_markets(vault_address)
        if vault_markets_unique_keys is None:
            return f"Vault with address {vault_address} not found."
        all_whitelisted_markets = get_whitelisted_markets()
        new_markets_for_vault = [
          market for market in all_whitelisted_markets
          if market.get('uniqueKey') not in vault_markets_unique_keys
        ]
        if new_markets_for_vault:
          result_lines = ["✅ New whitelisted markets not yet in your vault:"]
          for market in new_markets_for_vault:
            unique_key = market.get('uniqueKey', 'N/A')
            state = market.get('state', {})
            supply_apy_val = state.get('supplyApy')
            supply_apy = f"{supply_apy_val * 100:.2f}%" if supply_apy_val is not None else "N/A"
            loan_asset = market.get('loanAsset', {}).get('symbol', 'N/A')
            collateral_asset = market.get('collateralAsset', {}).get('symbol', 'N/A')
            result_lines.append(f"- Market: {loan_asset}/{collateral_asset}, Supply APY: {supply_apy}, Unique Key: {unique_key}")
          return "\n".join(result_lines)
        else:
          return "No new whitelisted markets found for your vault."
    except requests.exceptions.RequestException as e:
        return f"An error occurred while making the request: {e}"
    except (KeyError, TypeError) as e:
        return f"An error occurred while parsing the API response. The data structure may have changed: {e}"


if __name__ == "__main__":
    print(get_new_whitelisted_markets_for_vault())